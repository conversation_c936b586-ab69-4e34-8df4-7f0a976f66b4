<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản Trị - Classroom Web</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

</head>
<body>
    <!-- Header -->
    <header>
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="../index.html">
                        <i class="fas fa-graduation-cap"></i>
                        <span>Classroom Web</span>
                    </a>
                </div>
                <ul class="nav-menu">
                    <li><a href="../index.html">Trang chủ</a></li>
                    <li><a href="../classes/">L<PERSON><PERSON> học</a></li>
                    <li><a href="../auth/">Tài khoản</a></li>
                    <li><a href="index.html" class="active">Quản trị</a></li>
                </ul>
                <div class="hamburger">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Admin Section -->
    <section class="admin-section">
        <div class="container">
            <div class="admin-header">
                <h1><i class="fas fa-user-shield"></i> Trang Quản Trị</h1>
                <p>Quản lý người dùng và hệ thống</p>
            </div>
            <!-- Access Denied Message -->
            <div id="accessDenied" class="access-denied" style="display: none;">
                <div class="access-denied-content">
                    <i class="fas fa-ban"></i>
                    <h2>Truy cập bị từ chối</h2>
                    <p>Bạn không có quyền truy cập vào trang này.</p>
                    <a href="account.html" class="btn">Quay lại trang tài khoản</a>
                </div>
            </div>

            <!-- Admin Content -->
            <div id="adminContent" class="admin-content" style="display: none;">
                <!-- Admin Navigation -->
                <div class="admin-nav">
                    <button class="nav-btn active" onclick="showSection('userManagement')">
                        <i class="fas fa-users"></i> Quản Trị
                    </button>
                    <button class="nav-btn" onclick="showSection('classInspection')">
                        <i class="fas fa-chalkboard-teacher"></i> Kiểm tra Lớp học
                    </button>
                </div>

                <!-- User Management Section -->
                <div id="userManagement" class="admin-section active">
                    <!-- Statistics -->
                    <div class="admin-card">
                        <div class="card-header">
                            <h3><i class="fas fa-chart-bar"></i> Thống Kê</h3>
                        </div>
                        <div class="card-content">
                            <div class="stats-grid" id="statsGrid">
                                <!-- Stats will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- User Management -->
                    <div class="admin-card">
                        <div class="card-header">
                            <h3><i class="fas fa-users"></i> Quản Lý Người Dùng</h3>
                            <button id="refreshUsers" class="btn-refresh" onclick="loadOrphanedUsers()">
                                <i class="fas fa-sync-alt"></i> Làm mới
                            </button>
                        </div>
                        <div class="card-content">
                            <div class="search-box">
                                <input type="text" id="userSearch" placeholder="Tìm kiếm theo email hoặc tên...">
                                <i class="fas fa-search"></i>
                            </div>

                            <div class="admin-actions" style="margin-bottom: 20px;">
                                <button class="btn-refresh" onclick="loadOrphanedUsers()">
                                    <i class="fas fa-search"></i> Tải lại dữ liệu
                                </button>
                                <button class="btn-delete" onclick="cleanupAllOrphaned()" id="cleanupAllBtn" disabled>
                                    <i class="fas fa-trash"></i> Xóa tất cả người dùng
                                </button>
                            </div>

                            <div class="users-table-container">
                                <table class="users-table" id="orphanedUsersTable">
                                    <thead>
                                        <tr>
                                            <th>Email</th>
                                            <th>Tên</th>
                                            <th>Lớp học</th>
                                            <th>Ngày tạo</th>
                                            <th>Thao tác</th>
                                        </tr>
                                    </thead>
                                    <tbody id="orphanedUsersBody">
                                        <!-- Users will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                            <div id="loadingIndicator" class="loading-message" style="display: none;">
                                <i class="fas fa-spinner fa-spin"></i> Đang tải danh sách người dùng...
                            </div>
                            <div id="orphanedUsersContainer" class="no-data-message" style="display: none;">
                                <i class="fas fa-users-slash"></i> Không có người dùng nào
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Class Inspection Section -->
                <div id="classInspection" class="admin-section" style="display: none;">
                    <div class="admin-card">
                        <div class="card-header">
                            <h3><i class="fas fa-chalkboard-teacher"></i> Kiểm tra Lớp học</h3>
                            <p>Truy cập vào các lớp học để xem học viên và bài tập</p>
                        </div>
                        <div class="card-content">
                            <div class="class-grid">
                                <div class="class-card">
                                    <div class="class-icon">
                                        <i class="fab fa-python"></i>
                                    </div>
                                    <h4>Python - A</h4>
                                    <p>Thứ 7 - Chủ Nhật, 19:30 - 21:00</p>
                                    <div class="class-actions">
                                        <a href="../classes/python-a.html" class="btn-primary">
                                            <i class="fas fa-eye"></i> Xem lớp học
                                        </a>
                                        <a href="../classes/class-detail.html?class=python-a" class="btn-secondary">
                                            <i class="fas fa-tasks"></i> Xem bài tập
                                        </a>
                                    </div>
                                </div>

                                <div class="class-card">
                                    <div class="class-icon">
                                        <i class="fab fa-python"></i>
                                    </div>
                                    <h4>Python - B</h4>
                                    <p>Thứ 2 - Thứ 4, 19:30 - 21:00</p>
                                    <div class="class-actions">
                                        <a href="../classes/python-b.html" class="btn-primary">
                                            <i class="fas fa-eye"></i> Xem lớp học
                                        </a>
                                        <a href="../classes/class-detail.html?class=python-b" class="btn-secondary">
                                            <i class="fas fa-tasks"></i> Xem bài tập
                                        </a>
                                    </div>
                                </div>

                                <div class="class-card">
                                    <div class="class-icon">
                                        <i class="fab fa-python"></i>
                                    </div>
                                    <h4>Python - C</h4>
                                    <p>Thứ 3 - Thứ 5, 19:30 - 21:00</p>
                                    <div class="class-actions">
                                        <a href="../classes/python-c.html" class="btn-primary">
                                            <i class="fas fa-eye"></i> Xem lớp học
                                        </a>
                                        <a href="../classes/class-detail.html?class=python-c" class="btn-secondary">
                                            <i class="fas fa-tasks"></i> Xem bài tập
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loading -->
            <div id="adminLoading" class="admin-loading">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Đang kiểm tra quyền truy cập...</p>
            </div>
        </div>
    </section>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-exclamation-triangle"></i> Xác nhận xóa</h3>
                <span class="close" onclick="closeDeleteModal()">&times;</span>
            </div>
            <div class="modal-body">
                <p>Bạn có chắc chắn muốn xóa người dùng này?</p>
                <p><strong>Email:</strong> <span id="deleteUserEmail"></span></p>
                <p><strong>Tên:</strong> <span id="deleteUserName"></span></p>
                <p class="warning">⚠️ Hành động này không thể hoàn tác!</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-cancel" onclick="closeDeleteModal()">Hủy</button>
                <button class="btn btn-danger" id="confirmDeleteBtn">Xóa</button>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="successMessage" class="message success-message" style="display: none;"></div>
    <div id="errorMessage" class="message error-message" style="display: none;"></div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../assets/images/logo.jpg" alt="Vthon Logo">
                        <div class="logo-text">Vthon</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js';
        import { getAuth, onAuthStateChanged, signOut as firebaseSignOut } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js';
        import { getFirestore, doc, getDoc, collection, getDocs, deleteDoc, query, orderBy } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // UI Elements
        const adminLoading = document.getElementById('adminLoading');
        const accessDenied = document.getElementById('accessDenied');
        const adminContent = document.getElementById('adminContent');
        const usersTableBody = document.getElementById('orphanedUsersBody');
        const loadingIndicator = document.getElementById('loadingIndicator');
        const orphanedUsersContainer = document.getElementById('orphanedUsersContainer');
        const successMessage = document.getElementById('successMessage');
        const errorMessage = document.getElementById('errorMessage');

        // Global variables
        let currentOrphanedUsers = [];
        let userToDelete = null;

        // Check if user is admin
        function isAdmin(user) {
            return user && user.email === '<EMAIL>';
        }

        // Show success message
        function showSuccess(message) {
            successMessage.textContent = message;
            successMessage.style.display = 'block';
            errorMessage.style.display = 'none';
            setTimeout(() => {
                successMessage.style.display = 'none';
            }, 5000);
        }

        // Show error message
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            successMessage.style.display = 'none';
            setTimeout(() => {
                errorMessage.style.display = 'none';
            }, 5000);
        }

        // Show/hide loading
        function showLoading(show) {
            loadingIndicator.style.display = show ? 'block' : 'none';
        }

        // Hide messages
        function hideMessages() {
            successMessage.style.display = 'none';
            errorMessage.style.display = 'none';
        }

        // Check admin access
        onAuthStateChanged(auth, async (user) => {
            adminLoading.style.display = 'none';

            if (user) {
                // Check if user is admin using email
                if (isAdmin(user)) {
                    accessDenied.style.display = 'none';
                    adminContent.style.display = 'block';
                    loadStats();
                    console.log('Admin access granted for:', user.email);
                } else {
                    adminContent.style.display = 'none';
                    accessDenied.style.display = 'block';
                    console.log('Admin access denied for:', user.email);
                }
            } else {
                window.location.href = 'account.html';
            }
        });

        // Load database statistics and users
        async function loadStats() {
            try {
                // Load users from Firestore without ordering to avoid missing field issues
                const querySnapshot = await getDocs(collection(db, "users"));

                currentOrphanedUsers = [];
                querySnapshot.forEach((doc) => {
                    const userData = doc.data();
                    currentOrphanedUsers.push({
                        uid: doc.id,
                        ...userData
                    });
                });

                // Sort by createdAt if available, otherwise by email
                currentOrphanedUsers.sort((a, b) => {
                    if (a.createdAt && b.createdAt) {
                        return new Date(b.createdAt) - new Date(a.createdAt);
                    } else if (a.createdAt) {
                        return -1;
                    } else if (b.createdAt) {
                        return 1;
                    } else {
                        return (a.email || '').localeCompare(b.email || '');
                    }
                });

                // Count assignments
                let totalAssignments = 0;
                for (const user of currentOrphanedUsers) {
                    totalAssignments += user.assignmentCount || 0;
                }

                // Update stats
                const statsGrid = document.getElementById('statsGrid');
                statsGrid.innerHTML = `
                    <div class="stat-card">
                        <div class="stat-number" id="totalUsers">${currentOrphanedUsers.length}</div>
                        <div class="stat-label">Total Users</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="orphanedUsers">${currentOrphanedUsers.length}</div>
                        <div class="stat-label">Firestore Users</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalAssignments">${totalAssignments}</div>
                        <div class="stat-label">Total Assignments</div>
                    </div>
                `;

                // Display users in the table
                displayOrphanedUsers(currentOrphanedUsers);
                document.getElementById('cleanupAllBtn').disabled = currentOrphanedUsers.length === 0;

            } catch (error) {
                console.error("Error loading stats:", error);
                showError('Error loading user data: ' + error.message);
            }
        }

        // Load orphaned users (reload data)
        window.loadOrphanedUsers = async function() {
            showLoading(true);
            hideMessages();

            try {
                await loadStats(); // Reload all data

                if (currentOrphanedUsers.length > 0) {
                    showSuccess(`Found ${currentOrphanedUsers.length} users in database`);
                } else {
                    showSuccess('No users found in database');
                }

            } catch (error) {
                console.error('Error loading users:', error);
                showError('Error loading users: ' + error.message);
            } finally {
                showLoading(false);
            }
        };

        // Display users in table
        function displayOrphanedUsers(users) {
            usersTableBody.innerHTML = '';

            if (users.length === 0) {
                orphanedUsersContainer.style.display = 'block';
                return;
            }

            orphanedUsersContainer.style.display = 'none';

            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${user.email || 'N/A'}</td>
                    <td>${user.fullName || user.name || 'N/A'}</td>
                    <td>${user.courseClass || 'Chưa chọn'}</td>
                    <td>${user.createdAt ? new Date(user.createdAt).toLocaleDateString('vi-VN') : 'N/A'}</td>
                    <td>
                        <button class="btn-delete"
                                data-uid="${user.uid}"
                                data-email="${user.email || 'N/A'}"
                                data-name="${user.fullName || user.name || 'N/A'}">
                            <i class="fas fa-trash"></i> Xóa
                        </button>
                    </td>
                `;
                usersTableBody.appendChild(row);

                // Add event listener to the delete button
                const deleteBtn = row.querySelector('.btn-delete');
                deleteBtn.addEventListener('click', function() {
                    const uid = this.getAttribute('data-uid');
                    const email = this.getAttribute('data-email');
                    const name = this.getAttribute('data-name');
                    openDeleteModal(uid, email, name);
                });
            });
        }

        // Search users
        document.getElementById('userSearch').addEventListener('input', (e) => {
            const searchTerm = e.target.value.toLowerCase();
            const filteredUsers = currentOrphanedUsers.filter(user => {
                const email = (user.email || '').toLowerCase();
                const name = (user.fullName || user.name || '').toLowerCase();
                return email.includes(searchTerm) || name.includes(searchTerm);
            });
            displayOrphanedUsers(filteredUsers);
        });

        // Open delete modal
        window.openDeleteModal = function(uid, email, name) {
            console.log('=== OPEN DELETE MODAL DEBUG ===');
            console.log('UID:', uid);
            console.log('Email:', email);
            console.log('Name:', name);

            userToDelete = { uid, email, name };
            console.log('userToDelete set to:', userToDelete);

            document.getElementById('deleteUserEmail').textContent = email;
            document.getElementById('deleteUserName').textContent = name;
            document.getElementById('deleteModal').style.display = 'block';

            console.log('Modal should be visible now');
        };

        // Close delete modal
        window.closeDeleteModal = function() {
            document.getElementById('deleteModal').style.display = 'none';
            userToDelete = null;
        };

        // Confirm delete user
        document.getElementById('confirmDeleteBtn').addEventListener('click', async () => {
            console.log('=== CONFIRM DELETE BUTTON CLICKED ===');
            console.log('userToDelete:', userToDelete);

            if (!userToDelete) {
                console.error('ERROR: userToDelete is null or undefined');
                showError('Lỗi: Không tìm thấy thông tin người dùng cần xóa');
                return;
            }

            if (!userToDelete.uid) {
                console.error('ERROR: userToDelete.uid is missing');
                showError('Lỗi: Thiếu UID người dùng');
                return;
            }

            // Store userToDelete info before operations that might change it
            const userToDeleteInfo = {
                uid: userToDelete.uid,
                email: userToDelete.email,
                name: userToDelete.name
            };

            try {
                console.log('Attempting to delete user with UID:', userToDeleteInfo.uid);

                // Delete user document from Firestore
                await deleteDoc(doc(db, "users", userToDeleteInfo.uid));

                console.log('User deleted successfully from Firestore');

                showSuccess(`Đã xóa người dùng ${userToDeleteInfo.email} thành công`);
                closeDeleteModal();

                // Remove from current list and update display
                currentOrphanedUsers = currentOrphanedUsers.filter(user => {
                    return user && user.uid && user.uid !== userToDeleteInfo.uid;
                });
                displayOrphanedUsers(currentOrphanedUsers);

                // Update stats
                const statsElements = {
                    orphanedUsers: document.getElementById('orphanedUsers'),
                    totalUsers: document.getElementById('totalUsers'),
                    cleanupAllBtn: document.getElementById('cleanupAllBtn')
                };

                if (statsElements.orphanedUsers) {
                    statsElements.orphanedUsers.textContent = currentOrphanedUsers.length;
                }
                if (statsElements.totalUsers) {
                    statsElements.totalUsers.textContent = currentOrphanedUsers.length;
                }

                if (currentOrphanedUsers.length === 0 && statsElements.cleanupAllBtn) {
                    statsElements.cleanupAllBtn.disabled = true;
                }

            } catch (error) {
                console.error("Error deleting user:", error);
                showError('Lỗi khi xóa người dùng: ' + error.message);
            }
        });

        // Cleanup single user
        window.cleanupSingleUser = async function(uid) {
            const user = currentOrphanedUsers.find(u => u.uid === uid);
            const userEmail = user ? user.email : uid;

            if (!confirm(`Bạn có chắc chắn muốn xóa người dùng ${userEmail}?\n\nHành động này sẽ xóa:\n- Tài liệu người dùng trong Firestore\n- Tất cả dữ liệu liên quan\n\nHành động này không thể hoàn tác!`)) {
                return;
            }

            showLoading(true);
            hideMessages();

            try {
                // Delete user document from Firestore
                await deleteDoc(doc(db, "users", uid));

                showSuccess(`Đã xóa thành công người dùng ${userEmail}`);

                // Remove from current list and update display
                currentOrphanedUsers = currentOrphanedUsers.filter(user => user.uid !== uid);
                displayOrphanedUsers(currentOrphanedUsers);
                document.getElementById('orphanedUsers').textContent = currentOrphanedUsers.length;
                document.getElementById('totalUsers').textContent = currentOrphanedUsers.length;

                if (currentOrphanedUsers.length === 0) {
                    document.getElementById('cleanupAllBtn').disabled = true;
                    document.getElementById('orphanedUsersContainer').style.display = 'none';
                }

            } catch (error) {
                console.error('Error deleting user:', error);
                showError('Lỗi khi xóa người dùng: ' + error.message);
            } finally {
                showLoading(false);
            }
        };

        // Cleanup all orphaned users
        window.cleanupAllOrphaned = async function() {
            if (currentOrphanedUsers.length === 0) {
                showError('Không có người dùng nào để xóa');
                return;
            }

            if (!confirm(`Bạn có chắc chắn muốn xóa TẤT CẢ ${currentOrphanedUsers.length} người dùng?\n\nHành động này sẽ xóa:\n- Tất cả tài liệu người dùng trong Firestore\n- Tất cả dữ liệu liên quan\n\nHành động này không thể hoàn tác!`)) {
                return;
            }

            showLoading(true);
            hideMessages();

            try {
                let successCount = 0;
                let errorCount = 0;

                // Delete each user document
                for (const user of currentOrphanedUsers) {
                    try {
                        await deleteDoc(doc(db, "users", user.uid));
                        successCount++;
                    } catch (error) {
                        console.error(`Error deleting user ${user.uid}:`, error);
                        errorCount++;
                    }
                }

                if (successCount > 0) {
                    showSuccess(`Đã xóa thành công ${successCount} người dùng${errorCount > 0 ? `, ${errorCount} lỗi` : ''}`);
                } else {
                    showError('Không thể xóa người dùng nào');
                }

                // Clear the display
                currentOrphanedUsers = [];
                document.getElementById('orphanedUsersContainer').style.display = 'none';
                document.getElementById('cleanupAllBtn').disabled = true;
                document.getElementById('orphanedUsers').textContent = '0';
                document.getElementById('totalUsers').textContent = '0';

            } catch (error) {
                console.error('Error cleaning up all users:', error);
                showError('Lỗi khi xóa người dùng: ' + error.message);
            } finally {
                showLoading(false);
            }
        };

        // Show section function
        window.showSection = function(sectionName) {
            // Hide all sections
            const sections = document.querySelectorAll('.admin-section');
            sections.forEach(section => {
                section.style.display = 'none';
                section.classList.remove('active');
            });

            // Remove active class from all nav buttons
            const navButtons = document.querySelectorAll('.nav-btn');
            navButtons.forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected section
            const targetSection = document.getElementById(sectionName);
            if (targetSection) {
                targetSection.style.display = 'block';
                targetSection.classList.add('active');
            }

            // Add active class to clicked button
            const activeButton = event.target.closest('.nav-btn');
            if (activeButton) {
                activeButton.classList.add('active');
            }

            console.log('Switched to section:', sectionName);
        };

        // Sign out function
        window.signOut = async function() {
            try {
                await firebaseSignOut(auth);
                window.location.href = 'account.html';
            } catch (error) {
                console.error('Error signing out:', error);
            }
        };

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('deleteModal');
            if (event.target === modal) {
                closeDeleteModal();
            }
        };
    </script>

    <script src="../assets/js/script.js"></script>
</body>
</html>
