<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bảng Xếp <PERSON> - <PERSON>thon</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .page-header {
            text-align: center;
            margin: 120px 0 50px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
        }
        
        .page-header p {
            color: #666;
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
        }
        
        .leaderboard-container {
            max-width: 800px;
            margin: 0 auto 50px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .leaderboard-header {
            background-color: #111;
            color: white;
            padding: 15px 20px;
            display: grid;
            grid-template-columns: 80px 1fr 120px;
            align-items: center;
            font-weight: 500;
        }
        
        .leaderboard-item {
            padding: 20px;
            display: grid;
            grid-template-columns: 80px 1fr 120px;
            align-items: center;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s;
        }
        
        .leaderboard-item:hover {
            background-color: #f8f9fa;
        }
        
        .leaderboard-item:last-child {
            border-bottom: none;
        }
        
        .rank {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
            text-align: center;
        }
        
        .rank-1 {
            color: #FFD700; /* Gold */
        }
        
        .rank-2 {
            color: #C0C0C0; /* Silver */
        }
        
        .rank-3 {
            color: #CD7F32; /* Bronze */
        }
        
        .student-info {
            display: flex;
            align-items: center;
        }
        
        .student-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 15px;
            border: 2px solid #fff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .student-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .student-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }
        
        .student-class {
            color: #666;
            font-size: 0.9rem;
        }
        
        .score {
            font-weight: 700;
            color: #4285F4;
            font-size: 1.2rem;
            text-align: center;
        }
        
        .leaderboard-item:nth-child(odd) {
            background-color: #f8f9fa;
        }
        
        .leaderboard-item:nth-child(odd):hover {
            background-color: #f1f3f5;
        }
        
        .rank-badge {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            font-weight: 700;
            color: white;
        }
        
        .rank-badge-1 {
            background-color: #FFD700;
            box-shadow: 0 3px 10px rgba(255, 215, 0, 0.3);
        }
        
        .rank-badge-2 {
            background-color: #C0C0C0;
            box-shadow: 0 3px 10px rgba(192, 192, 192, 0.3);
        }
        
        .rank-badge-3 {
            background-color: #CD7F32;
            box-shadow: 0 3px 10px rgba(205, 127, 50, 0.3);
        }
        
        .rank-badge-other {
            background-color: #4285F4;
            box-shadow: 0 3px 10px rgba(66, 133, 244, 0.3);
        }
        
        .last-updated {
            text-align: center;
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 50px;
            font-style: italic;
        }
        
        @media (max-width: 768px) {
            .leaderboard-header,
            .leaderboard-item {
                grid-template-columns: 60px 1fr 80px;
                padding: 15px 10px;
            }
            
            .student-avatar {
                width: 40px;
                height: 40px;
                margin-right: 10px;
            }
            
            .student-name {
                font-size: 0.9rem;
            }
            
            .student-class {
                font-size: 0.8rem;
            }
            
            .score {
                font-size: 1rem;
            }
            
            .rank {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="../classes/">Lớp Học</a></li>
                    <li><a href="../achievements/">Thành Tích</a></li>
                    <li><a href="../auth/register.html">Đăng Ký</a></li>
                    <li><a href="index.html" class="active">Bảng Xếp Hạng</a></li>
                    <li><a href="../research/">Nghiên Cứu</a></li>
                    <li><a href="../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Rankings Section -->
    <section>
        <div class="container">
            <div class="page-header">
                <h1>Bảng Xếp Hạng Học Viên</h1>
                <p>Top 5 học viên có thành tích xuất sắc nhất dựa trên điểm tổng các bài kiểm tra</p>
            </div>
            
            <div class="leaderboard-container">
                <!-- No content placeholder -->
                <div style="text-align: center; padding: 100px 20px; color: #666;">
                    <i class="fas fa-trophy" style="font-size: 4rem; margin-bottom: 30px; color: #ddd;"></i>
                    <h3 style="font-size: 2rem; margin-bottom: 20px; color: #999;">Chưa có dữ liệu xếp hạng</h3>
                    <p style="font-size: 1.2rem; max-width: 600px; margin: 0 auto; line-height: 1.6;">
                        Bảng xếp hạng sẽ được cập nhật khi có học viên hoàn thành các bài kiểm tra và bài tập.
                        Hãy tham gia các lớp học và làm bài tập để xuất hiện trên bảng xếp hạng!
                    </p>
                    <div style="margin-top: 30px;">
                        <a href="classes.html" style="display: inline-block; background: #4285F4; color: white; padding: 12px 25px; border-radius: 8px; text-decoration: none; font-weight: 500; margin-right: 15px; transition: background-color 0.3s;">
                            <i class="fas fa-book"></i> Xem Lớp Học
                        </a>
                        <a href="../index.html" style="display: inline-block; background: #28a745; color: white; padding: 12px 25px; border-radius: 8px; text-decoration: none; font-weight: 500; transition: background-color 0.3s;">
                            <i class="fas fa-home"></i> Về Trang Chủ
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">VTA</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, collection, getDocs, query, orderBy, limit } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Load real rankings from Firebase
        async function loadRankings() {
            try {
                // Get all users first, then filter and sort
                const querySnapshot = await getDocs(collection(db, "users"));
                const rankings = [];

                querySnapshot.forEach((doc) => {
                    const userData = doc.data();
                    // Include users who have completed profile and have some score
                    if (userData.fullName && userData.fullName.trim() !== '') {
                        const totalScore = userData.totalScore || 0;
                        // Only include users with score > 0 for ranking
                        if (totalScore > 0) {
                            rankings.push({
                                name: userData.fullName,
                                class: userData.courseClass || 'Chưa phân lớp',
                                score: totalScore,
                                avatar: userData.avatar || '../assets/images/user.png',
                                lastAssignmentDate: userData.lastAssignmentDate || null
                            });
                        }
                    }
                });

                // Sort by score descending, then by date ascending (earlier completion wins ties)
                rankings.sort((a, b) => {
                    if (b.score !== a.score) {
                        return b.score - a.score;
                    }
                    // If scores are equal, earlier completion date wins
                    if (a.lastAssignmentDate && b.lastAssignmentDate) {
                        return new Date(a.lastAssignmentDate) - new Date(b.lastAssignmentDate);
                    }
                    return 0;
                });

                // Take top 5
                const topRankings = rankings.slice(0, 5);

                // Update the leaderboard
                updateLeaderboard(topRankings);

            } catch (error) {
                console.error("Error loading rankings:", error);
                // Show error state
                updateLeaderboard([]);
            }
        }

        function updateLeaderboard(rankings) {
            const leaderboardContainer = document.querySelector('.leaderboard-container');

            // Keep the header
            let html = `
                <div class="leaderboard-header">
                    <div>Hạng</div>
                    <div>Học Viên</div>
                    <div>Điểm Tổng</div>
                </div>
            `;

            if (rankings.length === 0) {
                html += `
                    <div class="leaderboard-item">
                        <div colspan="3" style="text-align: center; color: #666; grid-column: 1/-1;">
                            Chưa có dữ liệu xếp hạng
                        </div>
                    </div>
                `;
            } else {
                rankings.forEach((student, index) => {
                    const rank = index + 1;
                    const rankClass = rank <= 3 ? `rank-${rank}` : 'rank-other';
                    const className = getClassDisplayName(student.class);

                    html += `
                        <div class="leaderboard-item">
                            <div class="rank">
                                <div class="rank-badge rank-badge-${rank <= 3 ? rank : 'other'}">${rank}</div>
                            </div>
                            <div class="student-info">
                                <div class="student-avatar">
                                    <img src="${student.avatar}" alt="${student.name}">
                                </div>
                                <div>
                                    <div class="student-name">${student.name}</div>
                                    <div class="student-class">${className}</div>
                                </div>
                            </div>
                            <div class="score">${student.score}</div>
                        </div>
                    `;
                });
            }

            leaderboardContainer.innerHTML = html;

            // Update last updated time
            const lastUpdated = document.querySelector('.last-updated');
            if (lastUpdated) {
                const now = new Date();
                lastUpdated.textContent = `Cập nhật lần cuối: ${now.toLocaleDateString('vi-VN')}`;
            }
        }

        function getClassDisplayName(classId) {
            const classNames = {
                'python-a': 'Python - A',
                'python-b': 'Python - B',
                'python-c': 'Python - C'
            };
            return classNames[classId] || classId || 'Chưa phân lớp';
        }

        // Load rankings when authenticated or allow guest access
        document.addEventListener('DOMContentLoaded', function() {
            onAuthStateChanged(auth, (user) => {
                if (user) {
                    console.log('User authenticated, loading rankings:', user.email);
                    loadRankings();
                } else {
                    console.log('No user authenticated, loading rankings as guest');
                    // For guest users, we'll need to handle this differently
                    // For now, show a message that login is required
                    loadRankings(); // Try anyway, might work with updated rules
                }
            });
        });
    </script>

    <script src="../assets/js/script.js"></script>
</body>
</html>