<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>T<PERSON><PERSON> <PERSON><PERSON><PERSON> - <PERSON>thon</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(247,237,255,0.9) 50%, rgba(235,244,255,0.9) 100%);
        }
        
        .page-header {
            text-align: center;
            margin: 120px 0 30px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
        }
        
        .page-header p {
            color: #666;
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
        }
        
        .account-container {
            max-width: 500px;
            margin: 0 auto 50px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            position: relative;
            overflow: hidden;
            animation: fadeIn 0.8s ease-out forwards;
        }
        
        .account-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, #4285F4, #ff7aa8);
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h2 {
            color: #4285F4;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }
        
        .login-header p {
            color: #666;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            border-color: #4285F4;
            outline: none;
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
        }
        
        .form-actions {
            text-align: center;
            margin-top: 30px;
        }
        
        .account-btn {
            background: linear-gradient(to right, #4285F4, #ff7aa8);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.3s, box-shadow 0.3s;
            width: 100%;
        }
        
        .account-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 133, 244, 0.4);
        }
        
        .forgot-password {
            text-align: right;
            margin-top: 10px;
        }
        
        .forgot-password a {
            color: #4285F4;
            text-decoration: none;
            font-size: 0.9rem;
        }
        
        .forgot-password a:hover {
            text-decoration: underline;
        }
        
        .success-message,
        .error-message {
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
            font-weight: 500;
            animation: fadeIn 0.5s ease-out forwards;
            display: none;
            text-align: center;
        }
        
        .success-message {
            background-color: rgba(76, 217, 100, 0.1);
            color: #2ca745;
        }
        
        .error-message {
            background-color: rgba(255, 77, 77, 0.1);
            color: #ff4d4d;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }
        
        .loading i {
            color: #4285F4;
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .user-profile {
            display: none;
            animation: fadeIn 0.5s ease-out forwards;
        }
        
        .user-header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 20px;
            background-color: #f1f1f1;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .user-avatar:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(66, 133, 244, 0.3);
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .user-avatar i {
            font-size: 40px;
            color: white;
        }

        .avatar-change-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 50%;
        }

        .user-avatar:hover .avatar-change-overlay {
            opacity: 1;
        }

        .avatar-change-overlay i {
            color: white;
            font-size: 20px;
        }
        
        .user-info h2 {
            font-size: 1.5rem;
            margin-bottom: 5px;
        }
        
        .user-info p {
            color: #666;
            margin-bottom: 0;
        }
        
        .user-stats {
            display: flex;
            margin: 20px 0;
            border-top: 1px solid #eee;
            border-bottom: 1px solid #eee;
            padding: 15px 0;
        }
        
        .stat-item {
            flex: 1;
            text-align: center;
        }
        
        .stat-item .number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #4285F4;
            margin-bottom: 5px;
        }
        
        .stat-item .label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .action-btn {
            flex: 1;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
            border: 1px solid #eee;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #333;
            text-decoration: none;
        }
        
        .action-btn:hover {
            background: #f1f3f5;
            transform: translateY(-2px);
        }
        
        .action-btn i {
            margin-right: 8px;
            color: #4285F4;
        }
        
        .logout-btn {
            margin-top: 30px;
            text-align: center;
        }
        
        .logout-btn button {
            background: none;
            border: 1px solid #ff4d4d;
            color: #ff4d4d;
            padding: 8px 20px;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .logout-btn button:hover {
            background: rgba(255, 77, 77, 0.1);
        }
        
        /* Phần thông tin cá nhân */
        .profile-form {
            margin-top: 30px;
            border-top: 1px solid #eee;
            padding-top: 30px;
        }
        
        .profile-form h3 {
            margin-bottom: 20px;
            color: #333;
            font-size: 1.3rem;
            text-align: center;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }
        
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
            background-color: white;
        }
        
        .form-group select:focus {
            border-color: #4285F4;
            outline: none;
        }
        
        .form-note {
            margin-top: 10px;
            color: #666;
            font-size: 0.85rem;
            font-style: italic;
        }

        /* Avatar Selection Modal */
        .avatar-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }

        .avatar-modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            position: relative;
        }

        .avatar-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .avatar-modal-header h3 {
            color: #333;
            font-size: 1.5rem;
            margin: 0;
        }

        .avatar-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #999;
            transition: color 0.3s;
        }

        .avatar-close:hover {
            color: #333;
        }

        .avatar-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }

        .avatar-option {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            overflow: hidden;
            cursor: pointer;
            border: 3px solid transparent;
            transition: all 0.3s ease;
            position: relative;
        }

        .avatar-option:hover {
            transform: scale(1.1);
            border-color: #4285F4;
        }

        .avatar-option.selected {
            border-color: #4285F4;
            box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.3);
        }

        .avatar-option img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Profile Lock Notice */
        .profile-lock-notice {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 1px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
            text-align: center;
        }

        .profile-lock-notice h4 {
            color: #856404;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .profile-lock-notice p {
            color: #856404;
            margin: 0;
            font-size: 0.9rem;
        }

        .form-group.disabled input,
        .form-group.disabled select {
            background-color: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
            opacity: 0.7;
        }

        .form-group.disabled label {
            color: #6c757d;
        }

        /* Change Password Section */
        .change-password-section {
            margin-top: 30px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #ff7aa8;
            display: none;
        }

        .change-password-toggle {
            margin-top: 20px;
            background: linear-gradient(45deg, #ff7aa8, #4285F4);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .change-password-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 122, 168, 0.4);
        }

        .change-password-section h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .password-form {
            display: grid;
            gap: 15px;
        }

        .password-form .form-group {
            margin-bottom: 0;
        }

        .change-password-btn {
            background: linear-gradient(45deg, #ff7aa8, #4285F4);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            justify-self: start;
        }

        .change-password-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 122, 168, 0.4);
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="Vthon Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="../classes/">Lớp Học</a></li>
                    <li><a href="../achievements/">Thành Tích</a></li>
                    <li><a href="register.html">Đăng Ký</a></li>
                    <li><a href="../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../research/">Nghiên Cứu</a></li>
                    <li><a href="index.html" class="active">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Account Section -->
    <section>
        <div class="container">
            <div class="page-header">
                <h1>Tài Khoản Học Viên</h1>
                <p>Đăng nhập để truy cập tài liệu học tập và tham gia lớp học</p>
            </div>
            
            <div class="account-container">
                <!-- Login Form -->
                <div id="authForms">
                    <div class="login-header">
                        <h2>Đăng Nhập</h2>
                        <p>Sử dụng tài khoản được cung cấp từ giáo viên</p>
                    </div>
                    
                    <form id="loginForm">
                        <div class="form-group">
                            <label for="loginEmail">Email</label>
                            <input type="email" id="loginEmail" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="loginPassword">Mật khẩu</label>
                            <input type="password" id="loginPassword" required>
                        </div>
                        
                        <div class="forgot-password">
                            <a href="#" id="forgotPasswordLink">Quên mật khẩu?</a>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="account-btn">Đăng Nhập</button>
                        </div>
                    </form>
                    
                    <div class="loading">
                        <i class="fas fa-spinner"></i>
                        <p>Đang xử lý...</p>
                    </div>
                    
                    <div id="successMessage" class="success-message"></div>
                    <div id="errorMessage" class="error-message"></div>
                </div>
                
                <!-- User Profile (Shown after login) -->
                <div id="userProfile" class="user-profile">
                    <div class="user-header">
                        <div class="user-avatar" onclick="openAvatarModal()">
                            <img id="userAvatarImg" src="../assets/images/avatars/avatar_boy_1.png" alt="Avatar" style="display: none;">
                            <i class="fas fa-user" id="defaultAvatarIcon"></i>
                            <div class="avatar-change-overlay">
                                <i class="fas fa-camera"></i>
                            </div>
                        </div>
                        <div class="user-info">
                            <h2 id="userName">Tên Học Viên</h2>
                            <p id="userEmail"><EMAIL></p>
                        </div>
                    </div>
                    
                    <div class="user-stats">
                        <div class="stat-item">
                            <div class="number" id="userClassCount">0</div>
                            <div class="label">Lớp học</div>
                        </div>
                        <div class="stat-item">
                            <div class="number" id="userAssignmentCount">0</div>
                            <div class="label">Bài tập</div>
                        </div>
                        <div class="stat-item">
                            <div class="number" id="userRank">-</div>
                            <div class="label">Xếp hạng</div>
                        </div>
                    </div>
                    
                    <!-- Thông tin cá nhân của học viên -->
                    <div class="profile-form">
                        <h3>Thông Tin Cá Nhân</h3>

                        <!-- Profile Lock Notice (hidden by default) -->
                        <div id="profileLockNotice" class="profile-lock-notice" style="display: none;">
                            <h4><i class="fas fa-lock"></i> Thông tin đã được khóa</h4>
                            <p>Thông tin cá nhân của bạn đã được lưu và khóa trong <span id="lockDaysRemaining">7</span> ngày để đảm bảo tính chính xác. Bạn có thể chỉnh sửa lại sau <span id="unlockDate"></span>.</p>
                        </div>

                        <!-- Profile Success/Error Messages -->
                        <div id="profileSuccessMessage" class="success-message" style="display: none;"></div>
                        <div id="profileErrorMessage" class="error-message" style="display: none;"></div>

                        <form id="profileForm">
                            <!-- Admin Profile Fields (simplified) -->
                            <div id="adminProfileFields" style="display: none;">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="adminFullName">Họ và tên</label>
                                        <input type="text" id="adminFullName" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="adminGender">Giới tính</label>
                                        <select id="adminGender" required>
                                            <option value="">-- Chọn --</option>
                                            <option value="male">Nam</option>
                                            <option value="female">Nữ</option>
                                            <option value="other">Khác</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="adminBirthdate">Ngày sinh</label>
                                        <input type="date" id="adminBirthdate" required>
                                    </div>
                                </div>
                            </div>

                            <!-- Student Profile Fields (full form) -->
                            <div id="studentProfileFields">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="studentFullName">Họ và tên</label>
                                        <input type="text" id="studentFullName" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="studentGender">Giới tính</label>
                                        <select id="studentGender" required>
                                            <option value="">-- Chọn --</option>
                                            <option value="male">Nam</option>
                                            <option value="female">Nữ</option>
                                            <option value="other">Khác</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="studentBirthdate">Ngày sinh</label>
                                        <input type="date" id="studentBirthdate" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="studentId">Mã học sinh</label>
                                        <input type="text" id="studentId" required>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="school">Trường</label>
                                        <input type="text" id="school" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="schoolClass">Lớp</label>
                                        <input type="text" id="schoolClass" placeholder="VD: 10A1" required>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="parentName">Tên phụ huynh</label>
                                        <input type="text" id="parentName" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="parentPhone">SĐT phụ huynh</label>
                                        <input type="tel" id="parentPhone" required>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="courseClass">Lớp học quan tâm</label>
                                    <select id="courseClass" required>
                                        <option value="">-- Chọn lớp học --</option>
                                        <option value="python-a">Python - A (Thứ 7 - Chủ Nhật, 19:30 - 21:00)</option>
                                        <option value="python-b">Python - B (Thứ 2 - Thứ 4, 19:30 - 21:00)</option>
                                        <option value="python-c">Python - C (Thứ 3 - Thứ 5, 19:30 - 21:00)</option>
                                    </select>
                                    <p class="form-note">Chọn lớp học phù hợp với lịch học của bạn. Bạn có thể thay đổi lựa chọn này bất cứ lúc nào.</p>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="account-btn" id="saveProfileBtn">Lưu Thông Tin</button>
                            </div>
                        </form>

                        <!-- Change Password Toggle Button -->
                        <button type="button" class="change-password-toggle" id="changePasswordToggle">
                            <i class="fas fa-lock"></i> Đổi Mật Khẩu
                        </button>

                        <!-- Change Password Section -->
                        <div class="change-password-section" id="changePasswordSection">
                            <h3><i class="fas fa-lock"></i> Đổi Mật Khẩu</h3>
                            <form class="password-form" id="changePasswordForm">
                                <div class="form-group">
                                    <label for="currentPassword">Mật khẩu hiện tại</label>
                                    <input type="password" id="currentPassword" required>
                                </div>
                                <div class="form-group">
                                    <label for="newPassword">Mật khẩu mới</label>
                                    <input type="password" id="newPassword" required minlength="6">
                                </div>
                                <div class="form-group">
                                    <label for="confirmPassword">Xác nhận mật khẩu mới</label>
                                    <input type="password" id="confirmPassword" required minlength="6">
                                </div>
                                <button type="submit" class="change-password-btn">
                                    <i class="fas fa-key"></i> Đổi Mật Khẩu
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <div class="action-buttons" style="margin-top: 30px;">
                        <a href="../classes/" class="action-btn">
                            <i class="fas fa-book"></i> Lớp học của tôi
                        </a>
                        <a href="#" class="action-btn">
                            <i class="fas fa-tasks"></i> Bài tập
                        </a>
                        <!-- Admin button - only visible for admin users -->
                        <a href="../admin/" class="action-btn admin-btn" id="adminButton" style="display: none; background: linear-gradient(135deg, #e74c3c, #c0392b); color: white;">
                            <i class="fas fa-user-shield"></i> Quản Trị
                        </a>
                    </div>
                    
                    <div class="logout-btn">
                        <button id="logoutButton">
                            <i class="fas fa-sign-out-alt"></i> Đăng xuất
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Avatar Selection Modal -->
    <div id="avatarModal" class="avatar-modal">
        <div class="avatar-modal-content">
            <div class="avatar-modal-header">
                <h3><i class="fas fa-user-circle"></i> Chọn Avatar</h3>
                <button class="avatar-close" onclick="closeAvatarModal()">&times;</button>
            </div>
            <div class="avatar-grid" id="avatarGrid">
                <!-- Avatar options will be populated by JavaScript -->
            </div>
            <div style="text-align: center;">
                <button class="account-btn" onclick="saveSelectedAvatar()">
                    <i class="fas fa-save"></i> Lưu Avatar
                </button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../assets/images/logo.jpg" alt="Vthon Logo">
                        <div class="logo-text">Vthon</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script type="module">
        // Import the functions you need from the SDKs
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-analytics.js";
        import {
            getAuth,
            signInWithEmailAndPassword,
            signOut,
            onAuthStateChanged,
            sendPasswordResetEmail,
            createUserWithEmailAndPassword,
            updateProfile,
            updatePassword,
            reauthenticateWithCredential,
            EmailAuthProvider
        } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, doc, setDoc, getDoc, updateDoc, collection, getDocs } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const auth = getAuth(app);
        const db = getFirestore(app);
        
        // UI Elements
        const authForms = document.getElementById('authForms');
        const userProfile = document.getElementById('userProfile');
        const loadingElement = document.querySelector('.loading');
        const successMessage = document.getElementById('successMessage');
        const errorMessage = document.getElementById('errorMessage');
        const profileSuccessMessage = document.getElementById('profileSuccessMessage');
        const profileErrorMessage = document.getElementById('profileErrorMessage');
        const profileForm = document.getElementById('profileForm');
        const changePasswordForm = document.getElementById('changePasswordForm');
        const changePasswordToggle = document.getElementById('changePasswordToggle');
        const changePasswordSection = document.getElementById('changePasswordSection');

        // Avatar variables
        let selectedAvatar = '../assets/images/avatars/avatar_boy_1.png';
        const availableAvatars = [
            '../assets/images/avatars/avatar_boy_1.png',
            '../assets/images/avatars/avatar_boy_2.png',
            '../assets/images/avatars/avatar_businessman.png',
            '../assets/images/avatars/avatar_gamer.png',
            '../assets/images/avatars/avatar_girl.png',
            '../assets/images/avatars/avatar_man.png',
            '../assets/images/avatars/avatar_woman_1.png',
            '../assets/images/avatars/avatar_woman_2.png',
            '../assets/images/avatars/avatar_woman_3.png',
            '../assets/images/avatars/avatar_woman_4.png'
        ];

        // Profile lock variables
        let isProfileLocked = false;
        const PROFILE_LOCK_DAYS = 7;
        
        // Function to show/hide loading state
        function setLoading(isLoading) {
            loadingElement.style.display = isLoading ? 'block' : 'none';
        }

        // Profile Lock Functions
        function checkProfileLock(userData) {
            if (!userData.profileSavedAt) {
                return false; // No profile saved yet, not locked
            }

            const savedDate = new Date(userData.profileSavedAt);
            const currentDate = new Date();
            const daysDifference = Math.floor((currentDate - savedDate) / (1000 * 60 * 60 * 24));

            return daysDifference < PROFILE_LOCK_DAYS;
        }

        function updateProfileLockUI(userData) {
            const lockNotice = document.getElementById('profileLockNotice');
            const saveButton = document.getElementById('saveProfileBtn');
            const user = auth.currentUser;
            const userIsAdmin = isAdmin(user);

            if (userData.profileSavedAt && checkProfileLock(userData)) {
                // Profile is locked
                isProfileLocked = true;
                lockNotice.style.display = 'block';

                // Calculate remaining days and unlock date
                const savedDate = new Date(userData.profileSavedAt);
                const unlockDate = new Date(savedDate.getTime() + (PROFILE_LOCK_DAYS * 24 * 60 * 60 * 1000));
                const currentDate = new Date();
                const remainingDays = Math.ceil((unlockDate - currentDate) / (1000 * 60 * 60 * 24));

                document.getElementById('lockDaysRemaining').textContent = remainingDays;
                document.getElementById('unlockDate').textContent = unlockDate.toLocaleDateString('vi-VN');

                // Disable appropriate form fields based on user type
                if (userIsAdmin) {
                    const adminInputs = document.querySelectorAll('#adminProfileFields input, #adminProfileFields select');
                    adminInputs.forEach(input => {
                        input.disabled = true;
                        input.required = false;
                    });
                } else {
                    const studentInputs = document.querySelectorAll('#studentProfileFields input, #studentProfileFields select');
                    studentInputs.forEach(input => {
                        input.disabled = true;
                        input.required = false;
                    });
                }

                // Disable save button
                saveButton.disabled = true;
                saveButton.textContent = 'Thông tin đã khóa';
                saveButton.style.opacity = '0.6';
                saveButton.style.cursor = 'not-allowed';
            } else {
                // Profile is not locked
                isProfileLocked = false;
                lockNotice.style.display = 'none';

                // Enable appropriate form fields based on user type
                if (userIsAdmin) {
                    const adminInputs = document.querySelectorAll('#adminProfileFields input, #adminProfileFields select');
                    const studentInputs = document.querySelectorAll('#studentProfileFields input, #studentProfileFields select');

                    adminInputs.forEach(input => {
                        input.disabled = false;
                        input.required = true;
                    });

                    studentInputs.forEach(input => {
                        input.disabled = true;
                        input.required = false;
                    });
                } else {
                    const adminInputs = document.querySelectorAll('#adminProfileFields input, #adminProfileFields select');
                    const studentInputs = document.querySelectorAll('#studentProfileFields input, #studentProfileFields select');

                    adminInputs.forEach(input => {
                        input.disabled = true;
                        input.required = false;
                    });

                    studentInputs.forEach(input => {
                        input.disabled = false;
                        input.required = true;
                    });
                }

                // Enable save button
                saveButton.disabled = false;
                saveButton.textContent = 'Lưu Thông Tin';
                saveButton.style.opacity = '1';
                saveButton.style.cursor = 'pointer';
            }
        }

        // Test Firebase connection
        async function testFirebaseConnection() {
            try {
                console.log("Testing Firebase connection...");
                console.log("Auth:", auth);
                console.log("Database:", db);
                console.log("Current user:", auth.currentUser);

                if (auth.currentUser) {
                    console.log("User UID:", auth.currentUser.uid);
                    console.log("User email:", auth.currentUser.email);
                }
            } catch (error) {
                console.error("Firebase connection test failed:", error);
            }
        }
        
        // Function to show success message (for login/logout)
        function showSuccess(message) {
            console.log("=== SHOWSUCCESS DEBUG START ===");
            console.log("Message:", message);
            console.log("successMessage element:", successMessage);
            console.log("successMessage exists:", !!successMessage);

            if (!successMessage) {
                console.error("ERROR: successMessage element not found!");
                alert("Success: " + message); // Fallback
                return;
            }

            console.log("Setting message text...");
            successMessage.textContent = message;

            console.log("Setting display styles...");
            successMessage.style.display = 'block';
            successMessage.style.opacity = '1';
            successMessage.style.visibility = 'visible';
            successMessage.style.position = 'relative';
            successMessage.style.zIndex = '9999';
            successMessage.style.backgroundColor = 'rgba(76, 217, 100, 0.2)';
            successMessage.style.border = '2px solid #2ca745';
            successMessage.style.padding = '15px';
            successMessage.style.margin = '20px 0';
            successMessage.style.borderRadius = '5px';

            console.log("Hiding error message...");
            if (errorMessage) {
                errorMessage.style.display = 'none';
            }

            console.log("Current successMessage styles:");
            console.log("- display:", successMessage.style.display);
            console.log("- opacity:", successMessage.style.opacity);
            console.log("- visibility:", successMessage.style.visibility);
            console.log("- textContent:", successMessage.textContent);

            // Scroll to the message to ensure it's visible
            console.log("Scrolling to message...");
            successMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // Auto-hide after 10 seconds
            setTimeout(() => {
                console.log("Auto-fading message after 10 seconds");
                successMessage.style.opacity = '0.7';
            }, 10000);

            console.log("=== SHOWSUCCESS DEBUG END ===");
        }

        // Function to show profile success message
        function showProfileSuccess(message) {
            console.log("=== PROFILE SUCCESS DEBUG START ===");
            console.log("Message:", message);
            console.log("profileSuccessMessage element:", profileSuccessMessage);

            if (!profileSuccessMessage) {
                console.error("ERROR: profileSuccessMessage element not found!");
                alert("Success: " + message); // Fallback
                return;
            }

            // Set message content and styling
            profileSuccessMessage.textContent = message;
            profileSuccessMessage.style.display = 'block';
            profileSuccessMessage.style.opacity = '1';
            profileSuccessMessage.style.visibility = 'visible';
            profileSuccessMessage.style.position = 'relative';
            profileSuccessMessage.style.zIndex = '9999';
            profileSuccessMessage.style.backgroundColor = 'rgba(76, 217, 100, 0.2)';
            profileSuccessMessage.style.border = '2px solid #2ca745';
            profileSuccessMessage.style.padding = '15px';
            profileSuccessMessage.style.margin = '20px 0';
            profileSuccessMessage.style.borderRadius = '5px';
            profileSuccessMessage.style.color = '#2ca745';
            profileSuccessMessage.style.fontWeight = 'bold';

            // Hide profile error message
            if (profileErrorMessage) {
                profileErrorMessage.style.display = 'none';
            }

            // Scroll to the message
            profileSuccessMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // Auto-hide after 15 seconds
            setTimeout(() => {
                profileSuccessMessage.style.opacity = '0.8';
            }, 15000);

            console.log("Profile success message displayed successfully");
            console.log("=== PROFILE SUCCESS DEBUG END ===");
        }

        // Function to show profile error message
        function showProfileError(message) {
            console.log("=== PROFILE ERROR DEBUG START ===");
            console.log("Message:", message);

            if (!profileErrorMessage) {
                console.error("ERROR: profileErrorMessage element not found!");
                alert("Error: " + message); // Fallback
                return;
            }

            profileErrorMessage.textContent = message;
            profileErrorMessage.style.display = 'block';
            profileErrorMessage.style.opacity = '1';
            profileErrorMessage.style.visibility = 'visible';

            // Hide profile success message
            if (profileSuccessMessage) {
                profileSuccessMessage.style.display = 'none';
            }

            // Scroll to the message
            profileErrorMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });

            console.log("=== PROFILE ERROR DEBUG END ===");
        }
        
        // Function to show error message
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            successMessage.style.display = 'none';
        }
        
        // Login functionality
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            if (!email || !password) {
                showError('Vui lòng nhập đầy đủ thông tin');
                return;
            }
            
            setLoading(true);
            
            try {
                await signInWithEmailAndPassword(auth, email, password);
                showSuccess('Đăng nhập thành công!');
                setLoading(false);
            } catch (error) {
                setLoading(false);
                
                switch (error.code) {
                    case 'auth/wrong-password':
                    case 'auth/invalid-credential':
                        showError('Sai mật khẩu hoặc email');
                        break;
                    case 'auth/user-not-found':
                        showError('Tài khoản không tồn tại');
                        break;
                    default:
                        showError(`Lỗi đăng nhập: ${error.message}`);
                }
            }
        });
        
        // Password Reset
        document.getElementById('forgotPasswordLink').addEventListener('click', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('loginEmail').value;
            
            if (!email) {
                showError('Vui lòng nhập email để đặt lại mật khẩu');
                return;
            }
            
            setLoading(true);
            
            try {
                await sendPasswordResetEmail(auth, email);
                showSuccess('Đã gửi email đặt lại mật khẩu! Vui lòng kiểm tra hộp thư của bạn.');
                setLoading(false);
            } catch (error) {
                setLoading(false);
                showError(`Không thể gửi email đặt lại mật khẩu: ${error.message}`);
            }
        });
        
        // Logout functionality
        document.getElementById('logoutButton').addEventListener('click', async () => {
            try {
                await signOut(auth);
                showSuccess('Đã đăng xuất thành công');
            } catch (error) {
                showError(`Lỗi khi đăng xuất: ${error.message}`);
            }
        });

        // Avatar Functions
        function openAvatarModal() {
            const modal = document.getElementById('avatarModal');
            const avatarGrid = document.getElementById('avatarGrid');

            // Clear existing avatars
            avatarGrid.innerHTML = '';

            // Populate avatar options
            availableAvatars.forEach((avatarPath, index) => {
                const avatarOption = document.createElement('div');
                avatarOption.className = 'avatar-option';
                avatarOption.onclick = () => selectAvatar(avatarPath, avatarOption);

                const img = document.createElement('img');
                img.src = avatarPath;
                img.alt = `Avatar ${index + 1}`;

                avatarOption.appendChild(img);
                avatarGrid.appendChild(avatarOption);

                // Mark current avatar as selected
                if (avatarPath === selectedAvatar) {
                    avatarOption.classList.add('selected');
                }
            });

            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeAvatarModal() {
            const modal = document.getElementById('avatarModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function selectAvatar(avatarPath, element) {
            // Remove selected class from all options
            document.querySelectorAll('.avatar-option').forEach(option => {
                option.classList.remove('selected');
            });

            // Add selected class to clicked option
            element.classList.add('selected');
            selectedAvatar = avatarPath;
        }

        async function saveSelectedAvatar() {
            const user = auth.currentUser;
            if (!user) return;

            try {
                setLoading(true);

                // Update avatar in Firestore với merge option
                await setDoc(doc(db, "users", user.uid), {
                    avatar: selectedAvatar,
                    lastUpdated: new Date().toISOString()
                }, { merge: true });

                // Update UI
                const userAvatarImg = document.getElementById('userAvatarImg');
                const defaultAvatarIcon = document.getElementById('defaultAvatarIcon');

                userAvatarImg.src = selectedAvatar;
                userAvatarImg.style.display = 'block';
                defaultAvatarIcon.style.display = 'none';

                console.log("Avatar saved successfully:", selectedAvatar);
                showSuccess('Đã cập nhật avatar thành công!');
                closeAvatarModal();
                setLoading(false);
            } catch (error) {
                console.error("Error updating avatar:", error);
                showError('Lỗi khi cập nhật avatar: ' + error.message);
                setLoading(false);
            }
        }

        // Populate available classes in the dropdown
        async function populateCourseClasses() {
            try {
                const classSelect = document.getElementById('courseClass');

                // Classes are already defined in HTML, but we can ensure they're available
                // The classes are already populated in the HTML:
                // - Python - A (Thứ 7 - Chủ Nhật, 19:30 - 21:00)
                // - Python - B (Thứ 2 - Thứ 4, 19:30 - 21:00)
                // - Python - C (Thứ 3 - Thứ 5, 19:30 - 21:00)

                // Add change event listener to provide feedback when user selects a class
                classSelect.addEventListener('change', function() {
                    if (this.value) {
                        const selectedOption = this.options[this.selectedIndex];
                        console.log(`Đã chọn lớp: ${selectedOption.text}`);

                        // Show a subtle success message
                        const formNote = this.parentElement.querySelector('.form-note');
                        const originalText = formNote.textContent;
                        formNote.textContent = `✓ Đã chọn: ${selectedOption.text}`;
                        formNote.style.color = '#28a745';

                        // Reset after 3 seconds
                        setTimeout(() => {
                            formNote.textContent = originalText;
                            formNote.style.color = '#666';
                        }, 3000);
                    }
                });

                console.log("Available classes loaded successfully");
            } catch (error) {
                console.error("Error loading classes:", error);
            }
        }
        
        // Update Profile Form
        profileForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            console.log("Profile form submitted");

            const user = auth.currentUser;
            if (!user) {
                console.log("No user logged in");
                showProfileError('Bạn cần đăng nhập để cập nhật thông tin');
                return;
            }

            // Check if profile is locked
            if (isProfileLocked) {
                showProfileError('Thông tin cá nhân đang bị khóa. Vui lòng thử lại sau khi hết thời gian khóa.');
                return;
            }

            console.log("Current user:", user.uid, user.email);

            // Check if user is admin to determine which fields to save
            const userIsAdmin = isAdmin(user);
            let profileData;

            if (userIsAdmin) {
                // Admin profile - only save basic info
                const adminFullName = document.getElementById('adminFullName');
                const adminGender = document.getElementById('adminGender');
                const adminBirthdate = document.getElementById('adminBirthdate');

                profileData = {
                    fullName: adminFullName.value,
                    gender: adminGender.value,
                    birthdate: adminBirthdate.value,
                    avatar: selectedAvatar,
                    lastUpdated: new Date().toISOString(),
                    isAdmin: true,
                    profileSavedAt: new Date().toISOString()
                };

                // Validation for admin - only check enabled fields
                if (!adminFullName.disabled && !profileData.fullName) {
                    showProfileError('Vui lòng nhập họ và tên');
                    return;
                }
                if (!adminGender.disabled && !profileData.gender) {
                    showProfileError('Vui lòng chọn giới tính');
                    return;
                }
                if (!adminBirthdate.disabled && !profileData.birthdate) {
                    showProfileError('Vui lòng nhập ngày sinh');
                    return;
                }
            } else {
                // Student profile - save all fields
                const studentFullName = document.getElementById('studentFullName');
                const studentGender = document.getElementById('studentGender');
                const studentBirthdate = document.getElementById('studentBirthdate');
                const studentId = document.getElementById('studentId');
                const school = document.getElementById('school');
                const schoolClass = document.getElementById('schoolClass');
                const parentName = document.getElementById('parentName');
                const parentPhone = document.getElementById('parentPhone');
                const courseClass = document.getElementById('courseClass');

                profileData = {
                    fullName: studentFullName.value,
                    gender: studentGender.value,
                    birthdate: studentBirthdate.value,
                    studentId: studentId.value,
                    school: school.value,
                    schoolClass: schoolClass.value,
                    parentName: parentName.value,
                    parentPhone: parentPhone.value,
                    courseClass: courseClass.value,
                    avatar: selectedAvatar,
                    lastUpdated: new Date().toISOString(),
                    profileSavedAt: new Date().toISOString()
                };

                // Validation for student - only check enabled fields
                const requiredFields = [
                    {field: studentFullName, name: 'họ và tên'},
                    {field: studentGender, name: 'giới tính'},
                    {field: studentBirthdate, name: 'ngày sinh'},
                    {field: studentId, name: 'mã học sinh'},
                    {field: school, name: 'trường'},
                    {field: schoolClass, name: 'lớp'},
                    {field: parentName, name: 'tên phụ huynh'},
                    {field: parentPhone, name: 'số điện thoại phụ huynh'},
                    {field: courseClass, name: 'lớp học quan tâm'}
                ];

                for (const {field, name} of requiredFields) {
                    if (!field.disabled && !field.value.trim()) {
                        showProfileError(`Vui lòng điền ${name}`);
                        return;
                    }
                }
            }

            console.log("Profile data to save:", profileData);
            
            setLoading(true);
            
            try {
                console.log("=== PROFILE SAVE ATTEMPT DEBUG START ===");
                console.log("User UID:", user.uid);
                console.log("User email:", user.email);
                console.log("Profile data to save:", profileData);
                console.log("Firebase auth object:", auth);
                console.log("Firebase db object:", db);
                console.log("setDoc function:", typeof setDoc);
                console.log("doc function:", typeof doc);

                // Test Firebase connection first
                console.log("Testing Firebase connection...");
                const testDoc = doc(db, "users", user.uid);
                console.log("Test document reference created:", testDoc);

                // Cập nhật thông tin vào Firestore với merge option
                console.log("Attempting to save to Firestore...");
                const saveResult = await setDoc(testDoc, {
                    ...profileData,
                    email: user.email
                }, { merge: true });
                console.log("Firestore save result:", saveResult);
                console.log("✅ Firestore save successful!");

                // Cập nhật displayName trong Auth profile
                console.log("Updating Auth profile displayName...");
                await updateProfile(user, {
                    displayName: profileData.fullName
                });
                console.log("✅ Auth profile updated successfully!");

                console.log("=== PROFILE SAVE SUCCESS DEBUG START ===");
                console.log("Profile saved successfully:", profileData);
                console.log("Current loading state before setLoading(false):", loadingElement.style.display);

                // Ensure loading is turned off before showing success message
                setLoading(false);
                console.log("Loading turned off, current state:", loadingElement.style.display);

                // Add a small delay to ensure UI is ready
                console.log("Setting timeout for success message...");
                setTimeout(async () => {
                    console.log("Timeout executed, calling showProfileSuccess...");
                    console.log("About to show success message with text: 'Bạn đã cập nhật thông tin thành công! Vui lòng tải lại trang web để sử dụng các chức năng'");

                    // Test if showProfileSuccess function exists
                    console.log("showProfileSuccess function exists:", typeof showProfileSuccess);
                    console.log("profileSuccessMessage element exists:", !!profileSuccessMessage);

                    showProfileSuccess('Bạn đã cập nhật thông tin thành công! Vui lòng tải lại trang web để sử dụng các chức năng');
                    console.log("✅ showProfileSuccess called successfully");

                    // Reload user data to update profile lock UI
                    console.log("Reloading user data to update profile lock...");
                    try {
                        const userDoc = await getDoc(doc(db, "users", user.uid));
                        if (userDoc.exists()) {
                            const userData = userDoc.data();
                            console.log("Updated user data:", userData);
                            updateProfileLockUI(userData);
                            console.log("✅ Profile lock UI updated successfully");
                        }
                    } catch (error) {
                        console.error("❌ Error reloading user data:", error);
                    }

                    console.log("=== PROFILE SAVE SUCCESS DEBUG END ===");
                }, 100);
            } catch (error) {
                console.error("=== PROFILE SAVE ERROR DEBUG START ===");
                console.error("❌ Error updating profile:", error);
                console.error("Error code:", error.code);
                console.error("Error message:", error.message);
                console.error("Error stack:", error.stack);
                console.error("=== PROFILE SAVE ERROR DEBUG END ===");

                setLoading(false);

                // Provide more specific error messages
                let errorMessage = 'Lỗi khi cập nhật thông tin: ';
                if (error.code === 'permission-denied') {
                    errorMessage += 'Không có quyền truy cập. Vui lòng đăng nhập lại.';
                } else if (error.code === 'unavailable') {
                    errorMessage += 'Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối internet.';
                } else if (error.code === 'unauthenticated') {
                    errorMessage += 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.';
                } else {
                    errorMessage += error.message;
                }

                showProfileError(errorMessage);
            }
        });

        // Toggle Change Password Section
        changePasswordToggle.addEventListener('click', () => {
            if (changePasswordSection.style.display === 'none' || changePasswordSection.style.display === '') {
                changePasswordSection.style.display = 'block';
                changePasswordToggle.innerHTML = '<i class="fas fa-times"></i> Hủy Đổi Mật Khẩu';
            } else {
                changePasswordSection.style.display = 'none';
                changePasswordToggle.innerHTML = '<i class="fas fa-lock"></i> Đổi Mật Khẩu';
                // Reset form when hiding
                changePasswordForm.reset();
            }
        });

        // Change Password Form
        changePasswordForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const user = auth.currentUser;
            if (!user) {
                showError('Bạn cần đăng nhập để đổi mật khẩu');
                return;
            }

            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            // Validate passwords
            if (newPassword !== confirmPassword) {
                showError('Mật khẩu mới và xác nhận mật khẩu không khớp');
                return;
            }

            if (newPassword.length < 6) {
                showError('Mật khẩu mới phải có ít nhất 6 ký tự');
                return;
            }

            try {
                setLoading(true);

                // Re-authenticate user with current password
                const credential = EmailAuthProvider.credential(user.email, currentPassword);
                await reauthenticateWithCredential(user, credential);

                // Update password
                await updatePassword(user, newPassword);

                // Clear form
                changePasswordForm.reset();

                showSuccess('Mật khẩu đã được đổi thành công và cập nhật trên Firebase!');
                setLoading(false);
            } catch (error) {
                console.error("Error changing password:", error);
                if (error.code === 'auth/wrong-password') {
                    showError('Mật khẩu hiện tại không đúng');
                } else if (error.code === 'auth/weak-password') {
                    showError('Mật khẩu mới quá yếu');
                } else {
                    showError('Lỗi khi đổi mật khẩu: ' + error.message);
                }
                setLoading(false);
            }
        });

        // Check if user is admin
        function isAdmin(user) {
            console.log('=== ADMIN CHECK DEBUG ===');
            console.log('User object:', user);
            console.log('User email:', user ? user.email : 'No user');
            console.log('Expected admin email: <EMAIL>');
            const result = user && user.email === '<EMAIL>';
            console.log('Is admin result:', result);
            console.log('=== END ADMIN CHECK ===');
            return result;
        }

        // Update user profile UI
        async function updateUserProfile(user) {
            if (user) {
                // Show user profile, hide auth forms
                authForms.style.display = 'none';
                userProfile.style.display = 'block';

                // Load available course classes
                await populateCourseClasses();

                // Update user info
                document.getElementById('userName').textContent = user.displayName || 'Học viên';
                document.getElementById('userEmail').textContent = user.email;

                // Show admin button if user is admin
                const adminButton = document.getElementById('adminButton');
                console.log('=== ADMIN BUTTON DEBUG ===');
                console.log('adminButton element:', adminButton);
                console.log('adminButton exists:', !!adminButton);

                // Show/hide appropriate profile form based on admin status
                const adminProfileFields = document.getElementById('adminProfileFields');
                const studentProfileFields = document.getElementById('studentProfileFields');

                if (isAdmin(user)) {
                    if (adminButton) {
                        adminButton.style.display = 'block';
                        console.log('✅ Admin user detected, showing admin button');
                        console.log('Admin button display style:', adminButton.style.display);
                    } else {
                        console.error('❌ Admin button element not found!');
                    }

                    // Show admin form, hide student form
                    if (adminProfileFields && studentProfileFields) {
                        adminProfileFields.style.display = 'block';
                        studentProfileFields.style.display = 'none';

                        // Enable admin form fields and disable student form fields
                        const adminInputs = adminProfileFields.querySelectorAll('input, select');
                        const studentInputs = studentProfileFields.querySelectorAll('input, select');

                        adminInputs.forEach(input => {
                            input.disabled = false;
                            input.required = true;
                        });

                        studentInputs.forEach(input => {
                            input.disabled = true;
                            input.required = false;
                        });

                        console.log('✅ Showing admin profile form');
                    }
                } else {
                    if (adminButton) {
                        adminButton.style.display = 'none';
                        console.log('❌ Not admin user, hiding admin button');
                    }

                    // Show student form, hide admin form
                    if (adminProfileFields && studentProfileFields) {
                        adminProfileFields.style.display = 'none';
                        studentProfileFields.style.display = 'block';

                        // Enable student form fields and disable admin form fields
                        const adminInputs = adminProfileFields.querySelectorAll('input, select');
                        const studentInputs = studentProfileFields.querySelectorAll('input, select');

                        adminInputs.forEach(input => {
                            input.disabled = true;
                            input.required = false;
                        });

                        studentInputs.forEach(input => {
                            input.disabled = false;
                            input.required = true;
                        });

                        console.log('✅ Showing student profile form');
                    }
                }
                console.log('=== END ADMIN BUTTON DEBUG ===');
                
                // Try to fetch user data from Firestore
                try {
                    console.log("Fetching user data for UID:", user.uid);
                    const userDoc = await getDoc(doc(db, "users", user.uid));

                    if (userDoc.exists()) {
                        const userData = userDoc.data();
                        console.log("User data loaded:", userData);

                        // Cập nhật thống kê
                        document.getElementById('userClassCount').textContent = userData.classCount || 0;
                        document.getElementById('userAssignmentCount').textContent = userData.assignmentCount || 0;
                        document.getElementById('userRank').textContent = userData.rank || '-';
                        
                        // Điền thông tin vào form hồ sơ nếu có
                        if (isAdmin(user)) {
                            // Fill admin form fields
                            if (userData.fullName) document.getElementById('adminFullName').value = userData.fullName;
                            if (userData.gender) document.getElementById('adminGender').value = userData.gender;
                            if (userData.birthdate) document.getElementById('adminBirthdate').value = userData.birthdate;
                        } else {
                            // Fill student form fields
                            if (userData.fullName) document.getElementById('studentFullName').value = userData.fullName;
                            if (userData.gender) document.getElementById('studentGender').value = userData.gender;
                            if (userData.birthdate) document.getElementById('studentBirthdate').value = userData.birthdate;
                            if (userData.studentId) document.getElementById('studentId').value = userData.studentId;
                            if (userData.school) document.getElementById('school').value = userData.school;
                            if (userData.schoolClass) document.getElementById('schoolClass').value = userData.schoolClass;
                            if (userData.parentName) document.getElementById('parentName').value = userData.parentName;
                            if (userData.parentPhone) document.getElementById('parentPhone').value = userData.parentPhone;
                            if (userData.courseClass) document.getElementById('courseClass').value = userData.courseClass;
                        }

                        // Load avatar
                        if (userData.avatar) {
                            selectedAvatar = userData.avatar;
                            const userAvatarImg = document.getElementById('userAvatarImg');
                            const defaultAvatarIcon = document.getElementById('defaultAvatarIcon');

                            userAvatarImg.src = userData.avatar;
                            userAvatarImg.style.display = 'block';
                            defaultAvatarIcon.style.display = 'none';
                        }

                        // Update profile lock UI
                        updateProfileLockUI(userData);
                    } else {
                        // Create a new user document if it doesn't exist
                        console.log("Creating new user document for:", user.uid);
                        await setDoc(doc(db, "users", user.uid), {
                            name: user.displayName || 'Học viên',
                            email: user.email,
                            createdAt: new Date().toISOString(),
                            classCount: 0,
                            assignmentCount: 0,
                            rank: "-",
                            avatar: selectedAvatar
                        });
                        console.log("New user document created successfully");
                    }
                } catch (error) {
                    console.error("Error fetching user data:", error);
                }
            } else {
                // Show auth forms, hide user profile
                authForms.style.display = 'block';
                userProfile.style.display = 'none';
            }
        }
        
        // Auth state change listener
        onAuthStateChanged(auth, (user) => {
            updateUserProfile(user);
            testFirebaseConnection();
        });

        // Test function to check Firebase write permissions
        window.testFirebaseWrite = async function() {
            const user = auth.currentUser;
            if (!user) {
                console.log("❌ No user logged in for test");
                alert("❌ No user logged in for test");
                return;
            }

            try {
                console.log("=== FIREBASE WRITE TEST START ===");
                console.log("Testing user:", user.uid, user.email);
                console.log("Testing Firebase write...");

                const testData = {
                    testField: "test value",
                    timestamp: new Date().toISOString(),
                    testType: "manual_test"
                };

                console.log("Test data:", testData);

                await setDoc(doc(db, "users", user.uid), testData, { merge: true });

                console.log("✅ Firebase write test successful!");
                alert("✅ Firebase write test successful!");

                // Try to read back the data to confirm
                const readDoc = await getDoc(doc(db, "users", user.uid));
                if (readDoc.exists()) {
                    console.log("✅ Read back test successful:", readDoc.data());
                } else {
                    console.log("❌ Document doesn't exist after write");
                }

                console.log("=== FIREBASE WRITE TEST END ===");
            } catch (error) {
                console.error("=== FIREBASE WRITE TEST ERROR ===");
                console.error("❌ Firebase write test failed:", error);
                console.error("Error code:", error.code);
                console.error("Error message:", error.message);
                alert("❌ Firebase write test failed: " + error.message);
            }
        };

        // Test function for debugging success message
        window.testSuccessMessage = function() {
            console.log("=== TESTING SUCCESS MESSAGE ===");
            console.log("successMessage element:", successMessage);
            console.log("errorMessage element:", errorMessage);

            if (successMessage) {
                console.log("successMessage computed styles:");
                const styles = window.getComputedStyle(successMessage);
                console.log("- display:", styles.display);
                console.log("- visibility:", styles.visibility);
                console.log("- opacity:", styles.opacity);
                console.log("- position:", styles.position);
                console.log("- z-index:", styles.zIndex);
                console.log("- background-color:", styles.backgroundColor);
                console.log("- color:", styles.color);
            }

            showSuccess("Test message - if you see this, the function works!");
        };

        // Test function for debugging elements
        window.debugElements = function() {
            console.log("=== DEBUGGING ELEMENTS ===");
            console.log("successMessage by ID:", document.getElementById('successMessage'));
            console.log("errorMessage by ID:", document.getElementById('errorMessage'));
            console.log("profileForm by ID:", document.getElementById('profileForm'));
            console.log("loadingElement:", loadingElement);

            // Check if elements are in DOM
            console.log("successMessage in DOM:", document.body.contains(successMessage));
            console.log("errorMessage in DOM:", document.body.contains(errorMessage));
        };

        // Make functions global for HTML onclick
        window.openAvatarModal = openAvatarModal;
        window.closeAvatarModal = closeAvatarModal;
        window.saveSelectedAvatar = saveSelectedAvatar;

        // Close modal when clicking outside
        window.onclick = function(event) {
            const avatarModal = document.getElementById('avatarModal');
            if (event.target === avatarModal) {
                closeAvatarModal();
            }
        }
    </script>

    <script src="../assets/js/script.js"></script>
</body>
</html> 