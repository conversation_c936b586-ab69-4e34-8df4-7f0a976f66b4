<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python - A | Classroom Web</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .class-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
            margin-top: 80px; /* Add margin to avoid navigation overlap */
        }

        .class-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            word-wrap: break-word;
        }

        .class-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            word-wrap: break-word;
        }

        @media (max-width: 768px) {
            .class-header h1 {
                font-size: 2rem;
            }
            .class-header p {
                font-size: 1rem;
            }
        }

        .class-info {
            background: white;
            padding: 40px 0;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .info-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .info-card i {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 15px;
        }

        .info-card h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .access-denied {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
        }

        .access-granted {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
        }

        .lessons-section {
            background: #f8f9fa;
            padding: 40px 0;
        }

        .lesson-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .lesson-card h3 {
            color: #667eea;
            margin-bottom: 15px;
        }

        .meet-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            margin-top: 15px;
            transition: transform 0.3s ease;
        }

        .meet-link:hover {
            transform: translateY(-2px);
        }

        .students-display {
            margin-top: 20px;
        }

        .student-avatars {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .student-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
            position: relative;
            cursor: pointer;
        }

        .student-avatar:hover::after {
            content: attr(data-name);
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
        }

        .student-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="Vthon Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="index.html">Lớp Học</a></li>
                    <li><a href="../achievements/">Thành Tích</a></li>
                    <li><a href="../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../research/">Nghiên Cứu</a></li>
                    <li><a href="../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Class Header -->
    <section class="class-header">
        <div class="container">
            <h1><i class="fab fa-python"></i> Python - A</h1>
            <p>Lớp học Python cơ bản đến nâng cao - Thứ 7 & Chủ Nhật</p>
        </div>
    </section>

    <!-- Access Control Message -->
    <section class="class-info">
        <div class="container">
            <div id="accessMessage" style="display: none;"></div>
            
            <div id="classContent" style="display: none;">
                <!-- Class Information -->
                <div class="info-grid">
                    <div class="info-card">
                        <i class="fas fa-calendar-alt"></i>
                        <h3>Lịch Học</h3>
                        <p>Thứ 7 - Chủ Nhật<br>19:30 - 21:00</p>
                    </div>
                    <div class="info-card">
                        <i class="fas fa-users"></i>
                        <h3>Học Viên</h3>
                        <p id="studentCount">Đang tải...</p>
                        <div class="students-display">
                            <div id="studentAvatars" class="student-avatars"></div>
                        </div>
                    </div>
                    <div class="info-card">
                        <i class="fas fa-video"></i>
                        <h3>Google Meet</h3>
                        <a href="https://meet.google.com/ysi-jixy-qms" target="_blank" class="meet-link">
                            <i class="fas fa-video"></i> Tham gia lớp học
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Lessons Section -->
    <section class="lessons-section" id="lessonsSection" style="display: none;">
        <div class="container">
            <h2 style="text-align: center; margin-bottom: 40px;">Nội Dung Bài Học</h2>
            
            <div class="lesson-card">
                <h3><i class="fas fa-play-circle"></i> Bài 1: Chào Mừng Đến Với Kỷ Nguyên Số và Thế Giới Lập Trình</h3>
                <p><strong>Mục tiêu:</strong></p>
                <ul>
                    <li>Hiểu được khái niệm Công nghệ Thông tin (CNTT) và vai trò của nó trong đời sống.</li>
                    <li>Biết được một số lĩnh vực chính và cơ hội nghề nghiệp trong ngành CNTT.</li>
                    <li>Hiểu được Lập trình là gì và tại sao nên học lập trình.</li>
                </ul>
                <p><strong>Nội dung chính:</strong></p>
                <div style="margin-left: 20px;">
                    <p><strong>1. Tổng quan về Công nghệ Thông tin (CNTT):</strong></p>
                    <ul>
                        <li>CNTT là gì? Ví dụ thực tiễn (smartphone, Internet, mạng xã hội, game, ứng dụng học tập).</li>
                        <li>Tầm quan trọng và ảnh hưởng của CNTT tới xã hội hiện đại.</li>
                        <li>Các lĩnh vực chính trong CNTT (sơ lược): Phát triển phần mềm, Khoa học dữ liệu, An ninh mạng, Trí tuệ nhân tạo, v.v.</li>
                    </ul>
                    <p><strong>2. Giới thiệu về Lập trình:</strong></p>
                    <ul>
                        <li>Lập trình là gì? (Cách con người "nói chuyện" và "ra lệnh" cho máy tính).</li>
                        <li>Lợi ích của việc học lập trình: Phát triển tư duy logic, giải quyết vấn đề, sáng tạo.</li>
                        <li>Ngôn ngữ lập trình là gì? Giới thiệu sơ qua về sự đa dạng của các ngôn ngữ.</li>
                    </ul>
                </div>
                <p><strong>Thời lượng dự kiến:</strong> 1 buổi</p>
                <p><strong>Tài liệu:</strong> <a href="https://www.canva.com/design/DAGoEIUCxeE/f4G5xyktcg_yVi0fGpJlFw/edit?utm_content=DAGoEIUCxeE&utm_campaign=designshare&utm_medium=link2&utm_source=sharebutton" target="_blank" style="color: #667eea;">Xem tài liệu Canva</a></p>
                <p><strong>Trạng thái:</strong> <span id="assignmentStatus" style="color: #28a745;">Đang kiểm tra...</span></p>
                <a href="assignments/python-a/assignment-1.html" class="meet-link" id="assignmentLink">
                    <i class="fas fa-tasks"></i> <span id="assignmentLinkText">Làm bài tập</span>
                </a>
            </div>

            <div class="lesson-card">
                <h3><i class="fas fa-clock"></i> Bài 2: Sắp ra mắt</h3>
                <p><strong>Nội dung:</strong> Nội dung bài học tiếp theo đang được chuẩn bị...</p>
                <p><strong>Trạng thái:</strong> <span style="color: #6c757d;">Chưa có nội dung</span></p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="logo-text">Vthon</div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, doc, getDoc, collection, getDocs, query, where } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Check access permissions
        onAuthStateChanged(auth, async (user) => {
            const accessMessage = document.getElementById('accessMessage');
            const classContent = document.getElementById('classContent');
            const lessonsSection = document.getElementById('lessonsSection');

            if (user) {
                try {
                    const userDoc = await getDoc(doc(db, "users", user.uid));
                    const userData = userDoc.data();

                    // Check if user is admin
                    const isAdmin = userData && (userData.isAdmin || user.email === '<EMAIL>');
                    
                    // Check if user has access to Python-A class
                    const hasAccess = isAdmin || (userData && userData.courseClass === 'python-a');

                    if (hasAccess) {
                        // Grant access
                        accessMessage.innerHTML = `
                            <div class="access-granted">
                                <i class="fas fa-check-circle"></i>
                                <strong>Chào mừng ${isAdmin ? 'Admin' : 'học viên'}!</strong> 
                                Bạn có quyền truy cập vào lớp Python - A.
                                ${isAdmin ? '<br><small>Bạn đang ở chế độ Admin - có thể truy cập tất cả các lớp học.</small>' : ''}
                            </div>
                        `;
                        accessMessage.style.display = 'block';
                        classContent.style.display = 'block';
                        lessonsSection.style.display = 'block';

                        // Load student count and display
                        loadStudentData();

                        // Check assignment status
                        checkAssignmentStatus(user, isAdmin);
                    } else {
                        // Deny access
                        const userClass = userData?.courseClass || 'chưa chọn lớp';
                        accessMessage.innerHTML = `
                            <div class="access-denied">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>Không có quyền truy cập!</strong><br>
                                Bạn đã đăng ký lớp: <strong>${userClass}</strong><br>
                                Để truy cập lớp Python - A, vui lòng cập nhật thông tin lớp học trong trang 
                                <a href="../auth/">Tài Khoản</a>.
                            </div>
                        `;
                        accessMessage.style.display = 'block';
                    }
                } catch (error) {
                    console.error('Error checking user permissions:', error);
                    accessMessage.innerHTML = `
                        <div class="access-denied">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Lỗi kiểm tra quyền truy cập!</strong><br>
                            Vui lòng thử lại sau hoặc liên hệ admin.
                        </div>
                    `;
                    accessMessage.style.display = 'block';
                }
            } else {
                // User not logged in
                accessMessage.innerHTML = `
                    <div class="access-denied">
                        <i class="fas fa-sign-in-alt"></i>
                        <strong>Vui lòng đăng nhập!</strong><br>
                        Bạn cần đăng nhập để truy cập nội dung lớp học.
                        <br><a href="../auth/">Đăng nhập ngay</a>
                    </div>
                `;
                accessMessage.style.display = 'block';
            }
        });

        // Load student data for Python-A class
        async function loadStudentData() {
            try {
                const usersQuery = query(collection(db, "users"), where("courseClass", "==", "python-a"));
                const querySnapshot = await getDocs(usersQuery);
                const students = [];

                querySnapshot.forEach((doc) => {
                    const userData = doc.data();
                    if (userData.fullName) {
                        students.push({
                            name: userData.fullName,
                            avatar: userData.avatar || null
                        });
                    }
                });

                // Update student count
                document.getElementById('studentCount').textContent = `${students.length} học viên`;

                // Display student avatars (max 5)
                const avatarsContainer = document.getElementById('studentAvatars');
                avatarsContainer.innerHTML = '';

                const displayStudents = students.slice(0, 5);
                displayStudents.forEach(student => {
                    const avatarDiv = document.createElement('div');
                    avatarDiv.className = 'student-avatar';
                    avatarDiv.setAttribute('data-name', student.name);

                    if (student.avatar) {
                        const img = document.createElement('img');
                        img.src = student.avatar;
                        img.alt = student.name;
                        avatarDiv.appendChild(img);
                    } else {
                        // Show first letter of name if no avatar
                        avatarDiv.textContent = student.name.charAt(0).toUpperCase();
                    }

                    avatarsContainer.appendChild(avatarDiv);
                });

                // Show "+" if more than 5 students
                if (students.length > 5) {
                    const moreDiv = document.createElement('div');
                    moreDiv.className = 'student-avatar';
                    moreDiv.textContent = `+${students.length - 5}`;
                    moreDiv.setAttribute('data-name', `Và ${students.length - 5} học viên khác`);
                    avatarsContainer.appendChild(moreDiv);
                }

            } catch (error) {
                console.error('Error loading student data:', error);
                document.getElementById('studentCount').textContent = 'Không thể tải';
            }
        }

        // Check assignment completion status
        async function checkAssignmentStatus(user, isAdmin) {
            const statusElement = document.getElementById('assignmentStatus');
            const linkElement = document.getElementById('assignmentLink');
            const linkTextElement = document.getElementById('assignmentLinkText');

            if (!user) {
                statusElement.textContent = 'Cần đăng nhập';
                statusElement.style.color = '#6c757d';
                return;
            }

            try {
                // Check if assignment is completed
                const assignmentDoc = await getDoc(doc(db, "users", user.uid, "assignments", "assignment-1"));

                if (assignmentDoc.exists() && !isAdmin) {
                    // Assignment completed
                    const assignmentData = assignmentDoc.data();
                    const score = assignmentData.score || 0;
                    const totalQuestions = assignmentData.totalQuestions || 30;

                    statusElement.innerHTML = `Đã hoàn thành - Điểm: ${score}/${totalQuestions}`;
                    statusElement.style.color = '#28a745';

                    linkTextElement.textContent = 'Xem kết quả';
                    linkElement.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
                } else {
                    // Assignment not completed or user is admin
                    if (isAdmin) {
                        statusElement.textContent = 'Chế độ Admin - Có thể làm thử';
                        statusElement.style.color = '#dc3545';
                        linkTextElement.textContent = 'Làm thử (Admin)';
                        linkElement.style.background = 'linear-gradient(135deg, #dc3545, #c82333)';
                    } else {
                        statusElement.textContent = 'Chưa hoàn thành';
                        statusElement.style.color = '#ffc107';
                        linkTextElement.textContent = 'Làm bài tập';
                    }
                }
            } catch (error) {
                console.error('Error checking assignment status:', error);
                statusElement.textContent = 'Lỗi kiểm tra trạng thái';
                statusElement.style.color = '#dc3545';
            }
        }
    </script>

    <script src="../assets/js/script.js"></script>
</body>
</html>
